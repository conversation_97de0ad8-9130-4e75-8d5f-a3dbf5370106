import asyncio
import json
import logging
from typing import Dict, Any, Callable
import paho.mqtt.client as mqtt
import os
import threading
import time
from dotenv import load_dotenv

from mqtt.topics import MQTTTopics
from mqtt.handlers import MQTTMessageHandler
from services.data_service import DataService

load_dotenv()

logger = logging.getLogger(__name__)


class MQTTClient:
    def __init__(self):
        self.client = mqtt.Client()
        self.host = os.getenv("MQTT_BROKER_HOST",
                              "api.smarteye.smartflowtech.org")
        self.port = int(os.getenv("MQTT_BROKER_PORT", "1883"))
        self.username = os.getenv("MQTT_USERNAME")
        self.password = os.getenv("MQTT_PASSWORD")
        self.is_connected = False
        self.connection_result = None
        self.connection_event = threading.Event()
        self.event_loop = None  # Store reference to the main event loop
        self.last_ping_time = None
        self.last_message_time = None
        self.message_handler = MQTTMessageHandler()
        self.data_service = DataService()

        # Set up authentication if credentials are provided
        if self.username and self.password:
            self.client.username_pw_set(self.username, self.password)
            logger.info(
                f"MQTT authentication configured for user: {self.username}")
        else:
            logger.info("No MQTT authentication configured")

        # Set up callbacks
        self.client.on_connect = self._on_connect
        self.client.on_disconnect = self._on_disconnect
        self.client.on_message = self._on_message

    async def connect(self):
        """Connect to MQTT broker"""
        try:
            print(f"Connecting to MQTT broker at {self.host}:{self.port}...")
            logger.info(
                f"Attempting to connect to MQTT broker at {self.host}:{self.port}")

            # Store reference to the current event loop for message handling
            self.event_loop = asyncio.get_running_loop()

            # Reset connection state
            self.connection_result = None
            self.connection_event.clear()

            # Start the connection in a separate thread
            def connect_thread():
                try:
                    self.client.connect(self.host, self.port, 60)
                    self.client.loop_start()
                except Exception as e:
                    self.connection_result = e
                    self.connection_event.set()

            # Start connection thread
            thread = threading.Thread(target=connect_thread)
            thread.daemon = True
            thread.start()

            # Wait for connection result with timeout
            connection_timeout = 10  # seconds
            start_time = time.time()

            while time.time() - start_time < connection_timeout:
                if self.connection_event.is_set():
                    if self.connection_result == 0:  # Success
                        logger.info(
                            f"Successfully connected to MQTT broker at {self.host}:{self.port}")
                        return
                    else:  # Connection failed
                        raise ConnectionError(
                            f"MQTT connection failed with code {self.connection_result}")
                await asyncio.sleep(0.1)

            # Timeout occurred
            raise TimeoutError(
                f"Connection timeout after {connection_timeout} seconds")

        except Exception as e:
            print(f"Failed to connect to MQTT broker: {e}")
            logger.error(f"Failed to connect to MQTT broker: {e}")
            logger.warning(
                "Application will continue without MQTT connectivity")
            # Don't raise the exception to allow the application to start
            # The application can still function without MQTT for basic API operations

    async def disconnect(self):
        """Disconnect from MQTT broker"""
        self.client.loop_stop()
        self.client.disconnect()
        logger.info("Disconnected from MQTT broker")

    def _on_connect(self, client, userdata, flags, rc):
        """Callback for when client connects to broker"""
        if rc == 0:
            self.is_connected = True
            self.last_ping_time = time.time()
            print("✅ MQTT broker connected successfully!")
            logger.info("Successfully connected to MQTT broker")
            get_pumps = self.data_service.get_all_pumps_from_smarteye_core()
            # Subscribe to all relevant topics
            self._subscribe_to_topics()
        else:
            print(f"❌ MQTT connection failed with code {rc}")
            logger.error(
                f"Failed to connect to MQTT broker with result code {rc}")

        # Signal that connection attempt is complete
        self.connection_result = rc
        self.connection_event.set()

    def _on_disconnect(self, client, userdata, rc):
        """Callback for when client disconnects from broker"""
        self.is_connected = False
        print(f"MQTT broker disconnected (code: {rc})")
        logger.info("Disconnected from MQTT broker")

    def _on_message(self, client, userdata, msg):
        """Callback for when a message is received"""
        try:
            # Update last message timestamp
            self.last_message_time = time.time()
            print(msg.payload)
            topic = msg.topic
            payload = json.loads(msg.payload.decode())
            print(f"Received message on topic {topic}: {payload}")
            logger.info(f"Received message on topic {topic}: {payload}")

            # Handle the message asynchronously
            # Use the stored event loop reference for better performance
            if self.event_loop and not self.event_loop.is_closed():
                asyncio.run_coroutine_threadsafe(
                    self.message_handler.handle_message(topic, payload), self.event_loop)
            else:
                # Fallback: try to get current loop or create new one
                try:
                    loop = asyncio.get_running_loop()
                    asyncio.run_coroutine_threadsafe(
                        self.message_handler.handle_message(topic, payload), loop)
                except RuntimeError:
                    logger.warning(
                        "No running event loop found, creating new one for message handling")
                    asyncio.run(
                        self.message_handler.handle_message(topic, payload))

        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON payload: {e}")
        except Exception as e:
            logger.error(f"Error processing message: {e}")

    def _subscribe_to_topics(self):
        """Subscribe to all relevant MQTT topics"""
        topics = [
            f"{MQTTTopics.BASE}/tanks/+/logs",
            f"{MQTTTopics.BASE}/pump_logs",
            f"{MQTTTopics.BASE}/nozzles/+/status",
            f"{MQTTTopics.BASE}/nozzles/+/events",
            f"{MQTTTopics.BASE}/system/+",
        ]

        for topic in topics:
            self.client.subscribe(topic)
            logger.info(f"Subscribed to topic: {topic}")

    async def publish(self, topic: str, payload: Dict[str, Any], qos: int = 0, retain: bool = False):
        """Publish a message to MQTT broker"""
        try:
            if not self.is_connected:
                logger.warning(
                    "Not connected to MQTT broker, cannot publish message")
                return False

            json_payload = json.dumps(payload)
            result = self.client.publish(
                topic, json_payload, qos=qos, retain=retain)

            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                logger.info(f"Published message to topic {topic}: {payload}")
                return True
            else:
                logger.error(f"Failed to publish message to topic {topic}")
                return False

        except Exception as e:
            logger.error(f"Error publishing message: {e}")
            return False

    async def publish_device_config(self, device_id: str, config: Dict[str, Any]):
        """Publish device configuration"""
        from mqtt.topics import get_device_config_topic
        topic = get_device_config_topic(device_id)
        return await self.publish(topic, config, retain=True)

    # Connection monitoring methods
    def is_client_connected(self) -> bool:
        """Check if the MQTT client is connected (local status)"""
        return self.is_connected and self.client.is_connected()

    def get_connection_status(self) -> Dict[str, Any]:
        """Get comprehensive connection status information"""
        current_time = time.time()

        status = {
            "is_connected": self.is_client_connected(),
            "host": self.host,
            "port": self.port,
            "last_ping_time": self.last_ping_time,
            "last_message_time": self.last_message_time,
            "time_since_last_ping": None,
            "time_since_last_message": None,
            "connection_health": "unknown"
        }

        if self.last_ping_time:
            status["time_since_last_ping"] = current_time - self.last_ping_time

        if self.last_message_time:
            status["time_since_last_message"] = current_time - \
                self.last_message_time

        # Determine connection health
        if not status["is_connected"]:
            status["connection_health"] = "disconnected"
        elif status["time_since_last_ping"] and status["time_since_last_ping"] > 300:  # 5 minutes
            status["connection_health"] = "stale"
        else:
            status["connection_health"] = "healthy"

        return status

    async def ping_broker(self) -> bool:
        """Send a ping to the broker to test connectivity"""
        if not self.is_client_connected():
            return False

        try:
            # Publish to a test topic (you can customize this)
            test_topic = f"{MQTTTopics.BASE}/system/ping"
            result = await self.publish(test_topic, {
                "timestamp": time.time(),
                "client_id": "smarteye-device-hub",
                "type": "ping"
            })

            if result:
                self.last_ping_time = time.time()
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to ping broker: {e}")
            return False

    def get_last_activity_time(self) -> float:
        """Get the timestamp of the last activity (ping or message)"""
        times = [t for t in [self.last_ping_time,
                             self.last_message_time] if t is not None]
        return max(times) if times else 0

    def is_connection_stale(self, threshold_seconds: int = 300) -> bool:
        """Check if connection is stale (no activity for threshold_seconds)"""
        if not self.is_client_connected():
            return True

        last_activity = self.get_last_activity_time()
        if last_activity == 0:
            return True

        return (time.time() - last_activity) > threshold_seconds


# Global MQTT client instance
mqtt_client = MQTTClient()
